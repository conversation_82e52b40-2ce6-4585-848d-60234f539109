using DevExtreme.AspNet.Data.Helpers;
using DevExtreme.AspNet.Data;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using StarEIP.Models;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace StarEIP.Controllers
{
    [Route("api/EmailTemplates")]
    [ApiController]
    [Authorize]
    public class EmailTemplateController : ControllerBase
    {
        private readonly StarEipDbContext starEipDbContext;
        private readonly ILogger<EmailTemplateController> logger;

        public EmailTemplateController(StarEipDbContext starEipDbContext, ILogger<EmailTemplateController> logger)
        {
            this.starEipDbContext = starEipDbContext;
            this.logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            try
            {
                var templates = starEipDbContext.EmailTemplates;
                var loadOptions = new DataSourceLoadOptionsBase();
                DataSourceLoadOptionsParser.Parse(loadOptions, key => Request.Query.ContainsKey(key) ? Request.Query[key].FirstOrDefault() : null);

                loadOptions.PrimaryKey = [nameof(EmailTemplate.Name)];
                loadOptions.PaginateViaPrimaryKey = true;
                return Ok(await DataSourceLoader.LoadAsync(templates, loadOptions));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error in get users");
                throw;
            }
        }

        [HttpPut("Update")]
        public async Task<IActionResult> Update([FromForm] string key, [FromForm] string values)
        {
            var staff = await starEipDbContext.EmailTemplates.SingleAsync(r => r.Name == key);
            JsonConvert.PopulateObject(values, staff);
            await starEipDbContext.SaveChangesAsync();
            return Ok(staff);
        }

        [HttpPost("Create")]
        public async Task<IActionResult> Create([FromForm] string values)
        {
            var emailTemplate = new EmailTemplate { Body = "", Name = "", Subject = "" };
            JsonConvert.PopulateObject(values, emailTemplate);

            if (string.IsNullOrEmpty(emailTemplate.Name))
            {
                return BadRequest("EmailTemplate Name is required.");
            }

            await starEipDbContext.EmailTemplates.AddAsync(emailTemplate);
            await starEipDbContext.SaveChangesAsync();
            return Ok(emailTemplate);
        }
    }
}
